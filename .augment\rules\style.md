---
type: "agent_requested"
description: "style requirements of c code"
---
1. 使用的是windows10系统，脚本执行用cmd，不要用powershell
2. 请在生成代码时遵循以下C语言规范：
- 程序块要采用缩进风格编写，缩进的空格数为4个。
- 函数"{""}"各占单独一行，同时为了更好的在同一屏幕显示更多内容，判断语句"{""}"不再单独占一行
- 在每个函数结束之后都要加一行空行。
- 相对独立的程序块之间必须加空行。
- 一行程序以小于120字符为宜，较长的语句（>120 字符）要分成多行书写，长表达式要在低优先级操作符处划分新行，操作符放在新行之首，划分出的新行要跟上一行相应位置对齐，使排版整齐，语句可读。
- 若函数或过程中的参数较长，则要进行适当的划分.
- 不允许把多个短语句写在一行中，即一行只写一条语句.
- 在两个以上的关键字、变量、常量进行对等操作时，它们之间的操作符之前、之后或者前后要加空格；进行非对等操作时，如果是关系密切的立即操作符（如－>），后不应加空格. "*"（指针）、"!"、"~"、"++"、"–"、"&"（地址运算符）等单目操作符前后不加空格。"->"、"."前后不加空格。
- if、for、while、switch 等与后面的括号间应加空格，使if 等关键字更为突出、明显。
- 所有头文件都应该使用 #define 防止头文件被多重包含, 命名格式当是:__<PROJECT>_<PATH>_<FILE>_H__
- 使用标准的头文件包含顺序可增强可读性, 避免隐藏依赖: 相关头文件, C 库, 其他库的 .h, 本项目内的 .h。
- 保持c库兼容性，方便C++和C之间的相互调用
- if、for、do、while、case、switch、default 等语句自占一行，且if、for、do、while 等语句的执行语句部分无论多少都要加括号{}。
- 预处理符号‘#’总是置于第一列（对于#嵌套的情况有所例外）。
- 不要在 .h 文件中定义宏。
- 在马上要使用时才进行 #define, 使用后要立即 #undef。
- 不要只是对已经存在的宏使用#undef，选择一个不会冲突的名称；
- 不要试图使用展开后会导致 C 构造不稳定的宏。
- 不要用 ## 处理函数，类和变量的名字。
- 直接使用C99 的stdint.h 和stdbool.h 的标准类型定义，当遇到跨平台移植时，直接包含这两个头文件。
- 文件名要全部小写，多个单词用 (_)分隔，对于模块化（plugin/module）代码，文件名必须以lds_开头。
- 变量与函数名采用小驼峰式命名，
- 全局变量必须用 g 作为前缀，用static修饰
- 常量名称前加 k
- 宏命名单词全部大写，多个单词使用下划线分隔。
- 结构体类型名以lds_开头，以_t结尾，结构体数据成员命名和普通变量一样。
- 枚举类型名以LDS开头，以_E 结尾，和宏采用相同的命名方式。
- 联合体类型名以lds_开头，以_u结尾，联合体数据成员命名和普通变量一样。
- 回调函数命名lds开头，并以_t为结尾
- 按Doxygen规范注释代码，使用中文进行注释
- 只有当函数只有 5 行甚至更少时才将其定义为内联函数。
- 定义函数时, 参数顺序依次为: 输入参数，然后是输出参数，输入参数一般传值或传 const 引用， 输出参数或输入/输出参数则是非-const 指针。 对参数排序时，将只输入的参数放在所有输出参数之前。 
- 可以用的时候都要使用 const。
- 非传参时使用sizeof(varname) 代替sizeof(type)。
- 整数用 0，实数用 0.0， 指针用 NULL，字符 (串) 用 '\0'。
请记住这些规范，并在后续的代码生成中始终遵循。
