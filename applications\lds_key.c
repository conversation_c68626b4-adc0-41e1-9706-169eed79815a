#include "lds_button.h"
#include "lds_led.h"
#include "lds_multiple_pin_switch.h"
#include "lds_bk9535.h"
#include "lds_led_config.h"
#include "lds_uac.h"
#include "lds_digital_tube.h"
#include "lds_bk9535.h"
#include "lds_mic.h"
#include "lds_smart_base.h"

#include <rtthread.h>
#include <rtdevice.h>

#define DBG_TAG "key"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>


#define LDS_GPIO_POLL_NUM             4
#define LDS_MULTI_PIN_SWITCH_POLL_NUM 4
#define LDS_KEY_FILTER_COUNT          2

typedef enum
{
    USER_BUTTON_STD = 0,        //标准音效
    USER_BUTTON_BOY,            //男生音效
    USER_BUTTON_GIRL,           //女生音效
    USER_BUTTON_MAIN_MUTE,      //主麦静音
    USER_BUTTON_SUB_MUTE,       //从麦静音
    USER_BUTTON_MAX
} USER_BUTTON_E;

typedef struct 
{
    uint8_t count;              // 防抖计数
    int8_t value;               // 当前值
} lds_key_val_t;

static lds_button_t user_button[USER_BUTTON_MAX];
static const char *pin_names[USER_BUTTON_MAX] = {
    "PD.12",
    "PD.8",
    "PD.10",
    "PD.14",
    "PC.6",
};

static const char *g_gpioPollPins[LDS_GPIO_POLL_NUM] = {
    "PA.7",      // line in 1 放大倍数
    "PA.6",      // line in 2 放大倍数
    "PA.5",      // uhf发射功率
    "PA.4"       //闪避
};

static rt_base_t g_gpioPollPinNums[LDS_GPIO_POLL_NUM] = {0};
static lds_key_val_t g_gpioPollLastVals[LDS_GPIO_POLL_NUM] = {0};

static lds_multiple_pin_switch_t volume_switch;
static lds_multiple_pin_switch_t treble_switch;
static lds_multiple_pin_switch_t bass_switch;
static lds_multiple_pin_switch_t uhf_channel_switch;
static lds_multiple_pin_switch_t *g_multiPinSwitchPollPinNums[LDS_MULTI_PIN_SWITCH_POLL_NUM] = {0};
static lds_key_val_t g_multiPinSwitchPollLastVals[LDS_MULTI_PIN_SWITCH_POLL_NUM] = {0};

static const char *g_multiPinSwitchPollPins[LDS_MULTI_PIN_SWITCH_POLL_NUM] = {
    "PB.15,PB.14,PB.13,PB.12", // 音量
    "PE.7,PE.8,PE.9,PE.10",        // 高音
    "PE.11,PE.12,PE.13,PE.14",            // 低音
    "PE.4,PE.5,PE.6,PB.3,PB.4,PB.5"     // UHF频道
};

rt_base_t ldsKeyGpioInit(const char* pin_name, int mode)
{
    rt_base_t pin = rt_pin_get(pin_name);
    if (pin < 0)
    {
        LOG_E("Get pin %s failed %d !", pin_name, pin);
        return -RT_ERROR;
    }
    rt_pin_mode(pin, mode);
    return pin;
}

/**
 * @brief GPIO变化时的回调动作
 * @param pinIndex 变化的引脚索引
 * @param newVal 新的引脚值
 */
static void ldsGpioPollAction(size_t pinIndex, uint8_t newVal)
{
    // on时为低电平
    LOG_I("GPIO pin %s changed to %d", g_gpioPollPins[pinIndex], newVal);
    switch (pinIndex)
    {
        case 0: // line in 1 放大倍数
            if (newVal == 1)
            {
                LOG_I("Action for GPIO high");              
            }
            else
            {
                LOG_I("Action for GPIO low");
                // 执行相应动作
            }
            break;
        case 1: { // line in 2 放大倍数设置
            break;
        }
        case 2: {  //uhf发射功率
            LOG_D("Set BK9535 TX power to %s", newVal == 0 ? "high" : "low");
            lds_bk9535_set_reg0A_tx_power(newVal == 0);
            break;
        }
        case 3: {  //闪避
            LOG_D("Set mic select to %d", newVal);
            ldsSmartBaseSetSelectMode(newVal);
            break;
        }
        default:
            break;
    }
}

/**
 * @brief 初始化GPIO轮询引脚
 */
static void ldsGpioPollInit(void)
{
    rt_memset(g_gpioPollPinNums, 0, sizeof(g_gpioPollPinNums));
    rt_memset(g_gpioPollLastVals, -1, sizeof(g_gpioPollLastVals));
    for (size_t i = 0; i < LDS_GPIO_POLL_NUM; i++)
    {
        g_gpioPollPinNums[i] = ldsKeyGpioInit(g_gpioPollPins[i], PIN_MODE_INPUT_PULLUP);
        if (g_gpioPollPinNums[i] < 0)
        {
            LOG_E("Failed to init poll pin %s", g_gpioPollPins[i]);
            continue;
        }
        g_gpioPollLastVals[i].count = 0;  // 初始化为0
        g_gpioPollLastVals[i].value = -1; // 初始化为无效值
        LOG_I("GPIO poll pin %s initialized", g_gpioPollPins[i]);
    }
}

/**
 * @brief 轮询任务，检测GPIO电平变化
 */
static void ldsGpioPollTask(void)
{

    for (size_t i = 0; i < LDS_GPIO_POLL_NUM; i++)
    {
        if (g_gpioPollPinNums[i] < 0)
        {
            continue;
        }
        int8_t val = rt_pin_read(g_gpioPollPinNums[i]);
        if(val < 0){
            LOG_E("Error reading pin %d", g_gpioPollPinNums[i]);
            continue;
        }
        if ((val != g_gpioPollLastVals[i].value) && (g_gpioPollLastVals[i].count++ >= LDS_KEY_FILTER_COUNT))
        {
            g_gpioPollLastVals[i].count = 0;
            g_gpioPollLastVals[i].value = val;
            ldsGpioPollAction(i, val);
        }
    }
}

static void ldsMultiPinSwitchPollAction(size_t pinIndex, uint8_t newVal)
{
    LOG_I("switch %s changed to %d", g_multiPinSwitchPollPins[pinIndex], newVal);
    switch (pinIndex)
    {
        case 0: // 音量
            LOG_I("Action for vol switch %d", newVal);
            break;
        case 1: // 高音
            LOG_I("Action for treble switch %d", newVal);
            break;
        case 2: // 低音
            LOG_I("Action for bass switch %d", newVal);
            break;
        case 3: // uhf频道
            LOG_I("Action for uhf channel switch %d", newVal);
            if(lds_bk9535_set_reg0D_lookfor_table(newVal)) {
                newVal = 0xFF;
                ldsDigitalTubeSetDigit(newVal);
            } else {
                rt_thread_mdelay(1);
                lds_bk9535_set_reg_freq_after_init();
                ldsDigitalTubeSetNumber(newVal);
            }
            break;
        default:
            break;
    }
}
/**
 * @brief 初始化多引脚开关对象
 */
static void ldsMultiPinSwitchInit(void)
{
    rt_memset(g_multiPinSwitchPollPinNums, 0, sizeof(g_multiPinSwitchPollPinNums));
    rt_memset(g_multiPinSwitchPollLastVals, -1, sizeof(g_multiPinSwitchPollLastVals));

    int ret = ldsMultiplePinSwitchInit(&volume_switch, g_multiPinSwitchPollPins[0], 4, false, LDS_MULTIPLE_PIN_INPUT_NORMAL);
    if (ret != 0) {
        LOG_E("Failed to initialize lds_multiple_pin switch");
    }
    ret = ldsMultiplePinSwitchInit(&treble_switch, g_multiPinSwitchPollPins[1], 4, false, LDS_MULTIPLE_PIN_INPUT_NORMAL);
    if (ret != 0) {
        LOG_E("Failed to initialize lds_multiple_pin switch");
    }
    ret = ldsMultiplePinSwitchInit(&bass_switch, g_multiPinSwitchPollPins[2], 4, false, LDS_MULTIPLE_PIN_INPUT_NORMAL);
    if (ret != 0) {
        LOG_E("Failed to initialize lds_multiple_pin switch");
    }
    ret = ldsMultiplePinSwitchInit(&uhf_channel_switch, g_multiPinSwitchPollPins[3], 6, false, LDS_MULTIPLE_PIN_INPUT_PULL_UP);
    if (ret != 0) {
        LOG_E("Failed to initialize lds_multiple_pin switch");
    }

    g_multiPinSwitchPollPinNums[0] = &volume_switch;
    g_multiPinSwitchPollPinNums[1] = &treble_switch;
    g_multiPinSwitchPollPinNums[2] = &bass_switch;
    g_multiPinSwitchPollPinNums[3] = &uhf_channel_switch;
    for (size_t i = 0; i < LDS_MULTI_PIN_SWITCH_POLL_NUM; i++)
    {
        if (g_multiPinSwitchPollPinNums[i] == RT_NULL)
        {
            LOG_E("Failed to init multi pin switch %u", i);
            continue;
        }
        g_multiPinSwitchPollLastVals[i].count = 0;  // 初始化为0
        g_multiPinSwitchPollLastVals[i].value = -1; // 初始化为无效值
        LOG_I("Multi pin switch %u initialized", i);
    }
}

/**
 * @brief 轮询任务，检测多引脚开关值变化
 */
static void ldsMultiPinSwitchPollTask(void)
{

    for (size_t i = 0; i < LDS_MULTI_PIN_SWITCH_POLL_NUM; i++)
    {
        int8_t val = -1;
        if (g_multiPinSwitchPollPinNums[i] == RT_NULL)
        {
            continue;
        }
        val = ldsMultiplePinSwitchGet(g_multiPinSwitchPollPinNums[i]);
        if (val < 0) {
            LOG_E("Error reading lds_multiple_pin switch val");
            continue;
        }
        if ((val != g_multiPinSwitchPollLastVals[i].value) && (g_multiPinSwitchPollLastVals[i].count++ >= LDS_KEY_FILTER_COUNT))
        {
            g_multiPinSwitchPollLastVals[i].count = 0;
            g_multiPinSwitchPollLastVals[i].value = val;
            ldsMultiPinSwitchPollAction(i, val);
        }
    }    
}

static uint8_t common_btn_read(void *arg)
{
    uint8_t value = 0;

    lds_button_t *btn = (lds_button_t *)arg;
    rt_base_t pin = (rt_base_t)btn->usrData;
    if (pin < 0)
    {
        LOG_E("Button pin is not initialized!");
        return 0;
    }
    value = rt_pin_read(pin);
    return value;
}

/**
 * @brief 按键事件回调函数
 * @param arg 按键对象指针
 * @note 仅处理标准和男声按键组合，控制LED2状态
 */
static void common_btn_evt_cb(void *arg)
{
    lds_button_t *btn = (lds_button_t *)arg;

    LOG_I("Button %d event: %d", btn->id, btn->event);
    if (btn->event == LDS_BTN_PRESS_REPEAT_CLICK)
    {
        LOG_I("Button %d multiple click detected %d", btn->id, btn->clickCnt);
    }
    if (btn->event != LDS_BTN_PRESS_DOWN)
    {
        return;
    }
    // if ((ldsButtonEventRead(&user_button[USER_BUTTON_STD]) == LDS_BTN_PRESS_CLICK)
    //     && (ldsButtonEventRead(&user_button[USER_BUTTON_BOY]) == LDS_BTN_PRESS_CLICK))
    // {
    //     LOG_I("[combination]: button 0 and button 1\n");
    // }
    // if (ldsButtonEventRead(&user_button[USER_BUTTON_STD]) != LDS_BTN_PRESS_CLICK)
    // {
    //     return;
    // }
    //todo send the commnad
    switch(btn->id){
        case USER_BUTTON_STD:
            ldsLedOn(ldsLedGetConfigPin(LED_STD));
            ldsLedOff(ldsLedGetConfigPin(LED_BOY));
            ldsLedOff(ldsLedGetConfigPin(LED_GIRL));
            ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_STANDARD);
            break;
            case USER_BUTTON_BOY:
            ldsLedOn(ldsLedGetConfigPin(LED_BOY));
            ldsLedOff(ldsLedGetConfigPin(LED_STD));
            ldsLedOff(ldsLedGetConfigPin(LED_GIRL));
            ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_MALE);
            break;
            case USER_BUTTON_GIRL:
            ldsLedOn(ldsLedGetConfigPin(LED_GIRL));
            ldsLedOff(ldsLedGetConfigPin(LED_BOY));
            ldsLedOff(ldsLedGetConfigPin(LED_STD));
            ldsMicSetSoundMode(LDS_MIC_SOUND_MODE_FEMALE);
            break;
        case USER_BUTTON_MAIN_MUTE:
            ldsLedToggle(ldsLedGetConfigPin(LED_MAIN_MUTE));
            ldsMicSetMuteByAddr(LDS_MIC_ADDR_HOST);
            break;
            case USER_BUTTON_SUB_MUTE:
            ldsLedToggle(ldsLedGetConfigPin(LED_SUB_MUTE));
            ldsMicSetMuteByAddr(LDS_MIC_ADDR_SLAVE_BROADCAST);
            break;
        default:
            LOG_E("Button %d does not have a callback function", btn->id);
            break;
    }
    
}

static void buttonScan(void *arg)
{
    while(1)
    {
        ldsButtonScan();
        ldsGpioPollTask();
        ldsMultiPinSwitchPollTask();
        rt_thread_mdelay(20); // 20 ms
    }
}
void ldsKeyInit(void)
{
    rt_err_t ret = RT_EOK;
    rt_thread_t tid = RT_NULL;
    ldsGpioPollInit();
    ldsMultiPinSwitchInit();

    rt_memset(&user_button[0], 0x0, sizeof(user_button));
    
    for (size_t i = 0; i < USER_BUTTON_MAX; i++)
    {
        rt_base_t pin = ldsKeyGpioInit(pin_names[i], PIN_MODE_INPUT);
        if(pin < 0)
        {
            LOG_E("Failed to initialize button pin %s", pin_names[i]);
            continue;
        }
        user_button[i].id = i;
        user_button[i].usrButtonRead = common_btn_read;
        user_button[i].pressedLogicLevel = 0;
        user_button[i].shortPressStartTick = LDS_MS_TO_SCAN_CNT(1500);
        user_button[i].longPressStartTick = LDS_MS_TO_SCAN_CNT(3000);
        user_button[i].longHoldStartTick = LDS_MS_TO_SCAN_CNT(4500);
        user_button[i].usrData = (void *)pin; 
        user_button[i].cb = common_btn_evt_cb;

        ldsButtonRegister(&user_button[i]);
    }

    /* Create background ticks thread */
    tid = rt_thread_create("lds_btn", buttonScan, RT_NULL, 2048, 5, 20);
    if(tid == RT_NULL)
    {
        LOG_E("Create thread lds_btn failed!");
        return;
    }
    ret = rt_thread_startup(tid);
    if(ret != RT_EOK)
    {
        LOG_E("Thread lds_btn startup failed %d", ret);
        return;
    }
    LOG_I("Keys initialized");  
}





