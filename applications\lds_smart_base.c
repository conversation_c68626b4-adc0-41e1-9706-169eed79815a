/**
 * @file lds_smart_base.c
 * @brief LDS smart base Communication Protocol Stack Implementation
 * @details This file implements a complete communication protocol stack for smart base devices
 *          following the specified protocol format with big-endian byte order.
 *          This version includes a command queue for robust multi-command handling.
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-23
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * Protocol Format:
 * Head(2) + CMD(1) + SEQ(1) + LEN(2) + DATA(variable) + XOR(1)
 * - Big-endian byte order (MSB first)
 * - Head: Fixed 0x5AA5 for all frames
 * - SEQ: 0x00-0xFF sequence number for request/response matching
 * - CMD: Function code defining the operation
 * - LEN: Length of DATA field in bytes
 * - DATA: Variable length payload data
 * - XOR: Checkxor (xor of all bytes from Head to XOR-1, lower 8 bits)
 */

#include <rtdevice.h>
#include <stdlib.h>
#include "lds_utils.h"
#include "lds_uart.h"
#include "lds_smart_base.h"
#include "lds_led_config.h"
#include "lds_uac.h"

#define DBG_TAG "SMART_BASE"
#define DBG_LVL DBG_INFO
#include <rtdbg.h>

#define SMART_BASE_VERSION_MAX_LEN     16
/* ================================ Configuration ========================== */
#define SMART_BASE_SERIAL_NAME         "uart5"
#define SMART_BASE_POWER_CTRL_PIN      "PD.1"
#define SMART_BASE_CMD_QUEUE_SIZE      16                           /**< Command queue size, supporting up to 16 pending commands */
#define SMART_BASE_MAX_ERROR_COUNT     10                           /**< Maximum consecutive errors before asxore device is not connected */

/* ================================ Timeout Configuration ================== */
#define SMART_BASE_RESPONSE_TIMEOUT    (RT_TICK_PER_SECOND * 2)    /**< 2 seconds response timeout */
#define SMART_BASE_HEARTBEAT_TIMEOUT   (RT_TICK_PER_SECOND * 20)   /**< 20 seconds heartbeat timeout */
#define SMART_BASE_PARSE_TIMEOUT       (RT_TICK_PER_SECOND * 1)   /**< 1s parse timeout for state machine */
#define SMART_BASE_RETRY_COUNT         2                           /**< Maximum retry attempts */

/* ================================ Global Variables ======================= */
static rt_base_t g_smartBasePowerCtrl = -1;       /**< Smart base power control pin */
static rt_device_t g_smartBaseDev = RT_NULL;       /**< UART device handle */
static struct rt_timer g_heartbeatTimer;    /**< Heartbeat timeout timer */
static struct rt_timer g_retransmissionTimer; /**< Retransmission timer for command queue */
static struct rt_timer g_parseTimer;        /**< Parse timeout timer for state machine */
static struct rt_mutex g_smartBaseMutex;          /**< Thread safety mutex */
static uint8_t g_errorCount = 0 ;       /**< Error count for consecutive errors */
static bool g_enable = true;            /**< Enable flag for smart base communication */

/* ================================ Protocol State Machine ================ */
static lds_smart_base_frame_t g_rxFrame;           /**< Current receiving frame */
static uint8_t g_rxBuffer[SMART_BASE_MAX_FRAME_LEN]; /**< Frame receive buffer */
static uint16_t g_rxIndex = 0;              /**< Current receive index */
static uint8_t g_currentSeq = 0;            /**< Current sequence number */

/* ================================ Command Queue Management ================ */
/**
 * @brief Pending command queue entry structure
 * @details Contains all information for a command awaiting ACK, including retry management.
 */
typedef struct {
    bool active;                            /**< Defines if this queue slot is in use */
    uint8_t seq;                            /**< Unique sequence number for this command */
    uint8_t cmd;                           /**< Command code */
    uint16_t dataLen;                       /**< Length of the data payload */
    uint8_t data[SMART_BASE_MAX_DATA_LEN];         /**< Data payload */
    uint8_t retryCount;                     /**< Current retry count */
    rt_tick_t sent_timestamp;               /**< System tick when the command was last sent */
} lds_smart_base_cmd_queue_entry_t;

static lds_smart_base_cmd_queue_entry_t g_cmdQueue[SMART_BASE_CMD_QUEUE_SIZE]; /**< Command queue */
static void ldsSmartBaseStartRetransmissionTimer(void);
static int ldsSmartBaseSendFrame(uint8_t cmd, const uint8_t *data, uint16_t dataLen);
static void ldsSmartBaseResetStateMachine(void);

static int8_t g_smart_base_select_mode = -1;        //闪避模式 1开 0关
static int8_t g_smart_base_status_active = -1;    //是否收到手持麦克风声音 1有 0无
static bool g_smart_base_status_connect = false;   //是否连接上手持麦克风
static char g_smart_base_version[SMART_BASE_VERSION_MAX_LEN] = {0};

/**
 * @brief Smart base protocol state enumeration
 * @details Defines the states for the protocol frame parsing state machine
 */
typedef enum {
    SMART_BASE_STATE_IDLE = 0,        /**< Waiting for frame header */
    SMART_BASE_STATE_HEAD_1,          /**< Receiving head 1 0x5a */
    SMART_BASE_STATE_HEAD_2,          /**< Receiving head 2 0xa5 */
    SMART_BASE_STATE_CMD,           /**< Receiving command high byte */
    SMART_BASE_STATE_SEQ,             /**< Receiving sequence number */
    SMART_BASE_STATE_LEN_H,           /**< Receiving length high byte */
    SMART_BASE_STATE_LEN_L,           /**< Receiving length low byte */
    SMART_BASE_STATE_DATA,            /**< Receiving data payload */
    SMART_BASE_STATE_CHECKXOR,        /**< Receiving checkxor */
} LDS_SMART_BASE_STATE_E;

static LDS_SMART_BASE_STATE_E g_rxState = SMART_BASE_STATE_IDLE;

static void ldsSmartBaseSendSelectCmd(int8_t mode)
{
    LOG_I("send select cmd %d", mode);
    //todo send dsp cmd
    
}
int ldsSmartBaseSetSelectMode(int8_t mode)
{
    if(g_smart_base_select_mode != mode){
        g_smart_base_select_mode = mode;
        ldsSmartBaseSendSelectCmd(g_smart_base_select_mode);
    }
    return 0;
}
int ldsSmartBasePowerCtrl(bool on)
{
    if (g_smartBasePowerCtrl <= 0) {
        LOG_E("SMART_BASE power control pin not initialized");
        return -RT_ERROR;
    }
    rt_pin_write(g_smartBasePowerCtrl, on ? PIN_HIGH : PIN_LOW);
    g_enable = on;
    return 0;
}
/**
 * @brief Reset smart base device
 * @details Performs hardware reset of the smart base device via power control pin
 */
static void ldsSmartBaseReset(void)
{
    if (g_smartBasePowerCtrl <= 0) {
        LOG_E("SMART_BASE power control pin not initialized");
        return;
    }

    LOG_I("Resetting smart_base");
    rt_pin_write(g_smartBasePowerCtrl, PIN_LOW);
    rt_thread_mdelay(500);
    rt_pin_write(g_smartBasePowerCtrl, PIN_HIGH);
    rt_thread_mdelay(100);
}

/**
 * @brief Heartbeat timeout handler
 * @details Called when heartbeat timeout occurs, triggers device reset
 *
 * @param parameter Timer parameter (unused)
 */
static void ldsSmartBaseHeartbeatTimeout(void *parameter)
{
    LOG_W("heartbeat timeout, resetting device");
    ldsSmartBaseReset();
}

/**
 * @brief Parse timeout handler
 * @details Called when parse timeout occurs, resets the state machine to prevent hanging
 *
 * @param parameter Timer parameter (unused)
 */
static void ldsSmartBaseParseTimeout(void *parameter)
{
    LOG_W("Parse timeout state %d, reset", g_rxState);
    ldsSmartBaseResetStateMachine();
}

/**
 * @brief Initialize the command queue
 * @details Clears all entries in the command queue, marking them as inactive.
 */
static void ldsSmartBaseInitCmdQueue(void)
{
    rt_memset(&g_cmdQueue, 0, sizeof(g_cmdQueue));
}

/**
 * @brief Find the oldest pending command in the queue.
 * @details Iterates through the queue to find the active command with the earliest
 *          sent timestamp. This command is considered the "head" for retransmission.
 * @return Pointer to the oldest command entry, or RT_NULL if the queue is empty.
 */
static lds_smart_base_cmd_queue_entry_t* ldsSmartBaseFindOldestCmd(void)
{
    lds_smart_base_cmd_queue_entry_t *oldest_cmd = RT_NULL;
    rt_tick_t min_timestamp = RT_TICK_MAX;

    for (int i = 0; i < SMART_BASE_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            if (oldest_cmd == RT_NULL || (g_cmdQueue[i].sent_timestamp < min_timestamp)) {
                min_timestamp = g_cmdQueue[i].sent_timestamp;
                oldest_cmd = &g_cmdQueue[i];
            }
        }
    }
    return oldest_cmd;
}

/**
 * @brief Retransmission timeout handler.
 * @details This function is called when the retransmission timer expires. It handles
 *          the retransmission or dropping of the oldest command in the queue.
 * @param parameter Unused.
 */
static void ldsSmartBaseRetransmissionTimeout(void *parameter)
{
    uint8_t seq_bk = 0;
    rt_mutex_take(&g_smartBaseMutex, RT_WAITING_FOREVER);

    lds_smart_base_cmd_queue_entry_t *cmd_to_retry = ldsSmartBaseFindOldestCmd();

    if (cmd_to_retry != RT_NULL) {
        if (cmd_to_retry->retryCount >= SMART_BASE_RETRY_COUNT) {
            LOG_E("Max retries for cmd=0x%04X, seq=%d. Dropping.", cmd_to_retry->cmd, cmd_to_retry->seq);
            cmd_to_retry->active = false; // Drop the command
            g_errorCount ++;
            if(g_errorCount > SMART_BASE_MAX_ERROR_COUNT){
                LOG_D("SMART_BASE reach max error count,most likely not connected");
                g_enable = false;
            }
        } else {
            cmd_to_retry->retryCount++;
            LOG_W("Retrying cmd 0x%04X, seq=%d, attempt %d/%d",
                  cmd_to_retry->cmd, cmd_to_retry->seq, cmd_to_retry->retryCount, SMART_BASE_RETRY_COUNT);

            // Resend the command with the same sequence number
            seq_bk = g_currentSeq;
            g_currentSeq = cmd_to_retry->seq - 1; // ldsSmartBaseSendFrame will increment it back
            ldsSmartBaseSendFrame(cmd_to_retry->cmd, cmd_to_retry->data, cmd_to_retry->dataLen);
            cmd_to_retry->sent_timestamp = rt_tick_get();
            g_currentSeq = seq_bk;
        }
    }

    // After handling, always try to restart the timer for the next pending command
    ldsSmartBaseStartRetransmissionTimer();

    rt_mutex_release(&g_smartBaseMutex);
}

/**
 * @brief Starts the retransmission timer if there are pending commands.
 * @details Finds the oldest command and sets a one-shot timer for it.
 *          This function must be called with the mutex held.
 */
static void ldsSmartBaseStartRetransmissionTimer(void)
{
    rt_timer_stop(&g_retransmissionTimer);

    lds_smart_base_cmd_queue_entry_t *next_cmd = ldsSmartBaseFindOldestCmd();
    if (next_cmd != RT_NULL) {
        rt_tick_t timeout_tick = SMART_BASE_RESPONSE_TIMEOUT;
        // Optional: Can calculate remaining time if needed, but a fixed timeout is simpler.
        rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_SET_TIME, &timeout_tick);
        rt_timer_start(&g_retransmissionTimer);
    }
}

/**
 * @brief Get next sequence number
 * @details Generates the next sequence number for outgoing frames
 *
 * @return uint8_t Next sequence number (0x00-0xFF)
 */
static uint8_t ldsSmartBaseGetNextSeq(void)
{
    return ++g_currentSeq;
}

/**
 * @brief Send protocol frame
 * @details Constructs and sends a complete protocol frame with checkxor.
 *          Note: This is the low-level send function and does not manage the queue.
 *
 * @param cmd Command code (big-endian)
 * @param data Pointer to data payload (can be NULL if dataLen is 0)
 * @param dataLen Length of data payload
 * @return int 0 on success, negative error code on failure
 */
static int ldsSmartBaseSendFrame(uint8_t cmd, const uint8_t *data, uint16_t dataLen)
{
    uint8_t frame[SMART_BASE_MAX_FRAME_LEN];
    uint16_t frameLen;
    uint8_t checkxor;
    rt_size_t written;
    uint8_t seq = 0;
    
    if(!g_enable){
        LOG_D("SMART_BASE not enabled");
        return -RT_ERROR;
    }

    seq = ldsSmartBaseGetNextSeq();

    if (g_smartBaseDev == RT_NULL) {
        LOG_E("SMART_BASE device not initialized");
        return -RT_ERROR;
    }

    if (dataLen > SMART_BASE_MAX_DATA_LEN) {
        LOG_E("Data length %d exceeds maximum %d", dataLen, SMART_BASE_MAX_DATA_LEN);
        return -RT_EINVAL;
    }

    /* Construct frame */
    frameLen = 0;
    frame[frameLen++] = SMART_BASE_FRAME_HEAD1;          /* Head1 */
    frame[frameLen++] = SMART_BASE_FRAME_HEAD2;          /* Head2 */
    frame[frameLen++] = (uint8_t)(cmd >> 8);             /* CMD high byte */
    frame[frameLen++] = seq;                             /* SEQ */
    frame[frameLen++] = (uint8_t)(dataLen >> 8);         /* LEN high byte */
    frame[frameLen++] = (uint8_t)(dataLen & 0xFF);       /* LEN low byte */

    /* Copy data payload */
    if (data != RT_NULL && dataLen > 0) {
        rt_memcpy(&frame[frameLen], data, dataLen);
        frameLen += dataLen;
    }

    /* Calculate and append checkxor */
    checkxor = ldsUtilCheckXor(frame, frameLen);
    frame[frameLen++] = checkxor;

    /* Send frame */
    written = rt_device_write(g_smartBaseDev, 0, frame, frameLen);
    if (written != frameLen) {
        LOG_E("Failed to send complete frame, sent %d of %d bytes", written, frameLen);
        return -RT_ERROR;
    }

    LOG_D("Sent frame:  cmd=0x%04X, seq=%d, len=%d", cmd, seq, dataLen);
    LOG_HEX("smart_base-tx", 16, frame, frameLen);

    return seq; // Return the sequence number used
}

/**
 * @brief Sends a command and adds it to the pending queue.
 * @details This is the new main function for sending commands. It finds a free
 *          slot in the queue, sends the frame, and manages the retransmission timer.
 * @param cmd Command code
 * @param data Pointer to data payload
 * @param dataLen Length of data payload
 * @return 0 on success, negative error code on failure.
 */
static int ldsSmartBaseSendCommand(uint16_t cmd, const uint8_t *data, uint16_t dataLen)
{
    if(!g_enable){
        LOG_D("SMART_BASE not enabled");
        return -RT_ERROR;
    }

    rt_err_t result = rt_mutex_take(&g_smartBaseMutex, RT_WAITING_FOREVER);
    if (result != RT_EOK) {
        LOG_E("Failed to acquire mutex: %d", result);
        return -RT_ERROR;
    }

    int free_slot_idx = -1;
    for (int i = 0; i < SMART_BASE_CMD_QUEUE_SIZE; i++) {
        if (!g_cmdQueue[i].active) {
            free_slot_idx = i;
            break;
        }
    }

    if (free_slot_idx == -1) {
        LOG_E("Command queue is full. Cannot send cmd 0x%04X.", cmd);
        rt_mutex_release(&g_smartBaseMutex);
        return -RT_EBUSY;
    }

    // Backup current seq, as ldsSmartBaseSendFrame will modify it.
    uint8_t seq_bak = g_currentSeq;
    int seq_sent = ldsSmartBaseSendFrame(cmd, data, dataLen);

    if (seq_sent < 0) {
        g_currentSeq = seq_bak; // Restore seq on failure
        rt_mutex_release(&g_smartBaseMutex);
        return seq_sent; // Propagate error
    }

    // Populate queue entry
    lds_smart_base_cmd_queue_entry_t *entry = &g_cmdQueue[free_slot_idx];
    entry->active = true;
    entry->seq = (uint8_t)seq_sent;
    entry->cmd = cmd;
    entry->dataLen = dataLen;
    if (dataLen > 0) {
        rt_memcpy(entry->data, data, dataLen);
    }
    entry->retryCount = 0;
    entry->sent_timestamp = rt_tick_get();

    LOG_D("Cmd 0x%04X with seq=%d added to queue.", cmd, entry->seq);

    // If the timer is not running (i.e., queue was empty), start it.
    rt_uint8_t timer_state = 0;
    rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_GET_STATE, &timer_state);
    if (timer_state == RT_TIMER_FLAG_DEACTIVATED) {
        ldsSmartBaseStartRetransmissionTimer();
    }

    rt_mutex_release(&g_smartBaseMutex);
    return 0;
}


/**
 * @brief Process received protocol frame
 * @details Handles complete received frames and dispatches based on command type
 *
 * @param frame Pointer to received frame structure
 * @return int 0 on success, negative error code on failure
 */
static int ldsSmartBaseProcessFrame(const lds_smart_base_frame_t *frame)
{
    if (frame == RT_NULL) {
        LOG_E("Invalid frame pointer");
        return -RT_EINVAL;
    }

    LOG_D("Processing frame: cmd=0x%04X, seq=%d, len=%d",
          frame->cmd, frame->seq, frame->dataLen);

    /* Reset heartbeat timer on any valid frame */
    rt_timer_stop(&g_heartbeatTimer);
    rt_timer_start(&g_heartbeatTimer);

    g_errorCount = 0;
    g_enable = true;
    // This is an ACK or a response frame. Try to match it with a pending command.
    rt_mutex_take(&g_smartBaseMutex, RT_WAITING_FOREVER);
    bool ack_matched = false;
    for (int i = 0; i < SMART_BASE_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active && g_cmdQueue[i].seq == frame->seq) {
            LOG_D("ACK received for seq=%d. Removing from queue.", frame->seq);
            g_cmdQueue[i].active = false; // Deactivate the command
            ack_matched = true;
            
            // The command was acknowledged. We need to check if we should restart the timer
            // for the next oldest command.
            ldsSmartBaseStartRetransmissionTimer();
            break;
        }
    }
    if (!ack_matched) {
         LOG_W("Received ACK for unexpected seq=%d or command type 0x%04X", frame->seq, frame->cmd);
    }
    rt_mutex_release(&g_smartBaseMutex);


    switch (frame->cmd) {
        case LDS_SMART_BASE_CMD_KEY:
            LOG_D("Key command received: %d", frame->data[0]);
            ldsUacKeyCmdsend(frame->data[0] == 0x01);
            break;            
        case LDS_SMART_BASE_CMD_VERSION:
            if (frame->dataLen > 0) {
                int max_len = frame->dataLen > SMART_BASE_VERSION_MAX_LEN - 1 ? SMART_BASE_VERSION_MAX_LEN - 1 : frame->dataLen;
                LOG_I("Version info received: %.*s", frame->dataLen, frame->data);
                rt_memcpy(g_smart_base_version, frame->data, max_len);
                g_smart_base_version[max_len] = '\0';
            }
            break;

        case LDS_SMART_BASE_CMD_STATUS:
            g_smart_base_status_connect = frame->data[0] == 0x01;
            if(g_smart_base_status_active != frame->data[1]){
                g_smart_base_status_active = frame->data[1];
                if(g_smart_base_select_mode){
                    ldsSmartBaseSendSelectCmd(g_smart_base_select_mode);
                }
            }
            break;
        default:
            LOG_W("Unknown command received: 0x%04X", frame->cmd);
            return -RT_ERROR;
    }

    return 0;
}

/**
 * @brief Start parse timeout timer
 * @details Starts or restarts the parse timeout timer to prevent state machine hanging
 */
static void ldsSmartBaseStartParseTimer(void)
{
    rt_timer_stop(&g_parseTimer);
    rt_timer_start(&g_parseTimer);
}

/**
 * @brief Reset frame parsing state machine
 * @details Resets the state machine to idle state and clears buffers
 */
static void ldsSmartBaseResetStateMachine(void)
{
    /* Stop parse timeout timer */
    rt_timer_stop(&g_parseTimer);

    g_rxState = SMART_BASE_STATE_IDLE;
    g_rxIndex = 0;
    rt_memset(&g_rxFrame, 0, sizeof(g_rxFrame));
    rt_memset(g_rxBuffer, 0, sizeof(g_rxBuffer));
}

/**
 * @brief Protocol frame parsing state machine
 * @details Parses incoming bytes according to the protocol specification
 *
 * @param data Pointer to received data buffer
 * @param size Size of received data
 * @return int 0 on success, negative error code on failure
 */
static int ldsSmartBaseParseData(const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    for (rt_size_t i = 0; i < size; i++) {
        uint8_t byte = data[i];
        LOG_D("state %d", g_rxState);
        switch (g_rxState) {
            case SMART_BASE_STATE_IDLE:
                if (byte == SMART_BASE_FRAME_HEAD1) {
                    ldsSmartBaseResetStateMachine();
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head1 = byte;
                    g_rxState = SMART_BASE_STATE_HEAD_1;
                    /* Start parse timeout timer when entering parsing state */
                    ldsSmartBaseStartParseTimer();
                }
                break;

            case SMART_BASE_STATE_HEAD_1:
                if (byte == SMART_BASE_FRAME_HEAD2) {
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head2 = byte;
                    g_rxState = SMART_BASE_STATE_CMD;
                    /* Start parse timeout timer when entering parsing state */
                    ldsSmartBaseStartParseTimer();
                }
                break;

            case SMART_BASE_STATE_CMD:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.cmd = byte;
                g_rxState = SMART_BASE_STATE_SEQ;
                ldsSmartBaseStartParseTimer();
                break;

            case SMART_BASE_STATE_SEQ:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.seq = byte;
                g_rxState = SMART_BASE_STATE_LEN_H;
                ldsSmartBaseStartParseTimer();
                break;

            case SMART_BASE_STATE_LEN_H:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen = (uint16_t)(byte << 8);
                g_rxState = SMART_BASE_STATE_LEN_L;
                ldsSmartBaseStartParseTimer();
                break;

            case SMART_BASE_STATE_LEN_L:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen |= byte;

                /* Validate data length */
                if (g_rxFrame.dataLen > SMART_BASE_MAX_DATA_LEN) {
                    LOG_E("Invalid data length: %d", g_rxFrame.dataLen);
                    ldsSmartBaseResetStateMachine();
                    break;
                }

                if (g_rxFrame.dataLen == 0) {
                    g_rxState = SMART_BASE_STATE_CHECKXOR;
                } else {
                    g_rxState = SMART_BASE_STATE_DATA;
                }
                ldsSmartBaseStartParseTimer();
                break;

            case SMART_BASE_STATE_DATA:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.data[g_rxIndex - 10] = byte;  /* Data starts at index 9 + 1 for checkxor */

                if (g_rxIndex >= (9 + g_rxFrame.dataLen)) {
                    g_rxState = SMART_BASE_STATE_CHECKXOR;
                }
                ldsSmartBaseStartParseTimer();
                break;

            case SMART_BASE_STATE_CHECKXOR:
                g_rxFrame.xor = byte;
                g_rxBuffer[g_rxIndex++] = byte; // Also add checkxor to buffer for verification

                /* Validate checkxor */
                uint8_t calculatedCheckxor = ldsUtilCheckXor(g_rxBuffer, g_rxIndex - 1);
                if (calculatedCheckxor != g_rxFrame.xor) {
                    LOG_E("Checkxor mismatch: calculated=0x%02X, received=0x%02X",
                          calculatedCheckxor, g_rxFrame.xor);
                    LOG_HEX("smart_base-rx-err", 16, g_rxBuffer, g_rxIndex);
                    ldsSmartBaseResetStateMachine();
                    break;
                }

                LOG_HEX("smart_base-rx", 16, g_rxBuffer, g_rxIndex);
                ldsSmartBaseProcessFrame(&g_rxFrame);
                ldsSmartBaseResetStateMachine();
                break;

            default:
                LOG_E("Invalid state: %d", g_rxState);
                ldsSmartBaseResetStateMachine();
                break;
        }

        /* Prevent buffer overflow */
        if (g_rxIndex >= SMART_BASE_MAX_FRAME_LEN) {
            LOG_E("Frame buffer overflow");
            ldsSmartBaseResetStateMachine();
            break;
        }
    }

    return 0;
}

/**
 * @brief UART data processing callback
 * @details Callback function registered with UART driver for data processing
 *
 * @param dev RT-Thread device handle
 * @param data Pointer to received data buffer
 * @param size Size of received data in bytes
 * @return int 0 on success, negative error code on failure
 */
int ldsSmartBaseProcess(rt_device_t dev, const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    if (dev == RT_NULL) {
        LOG_E("Invalid device handle");
        return -RT_EINVAL;
    }

    LOG_D("Received %d bytes from %s", size, dev->parent.name);

    // No mutex here, parse happens in UART's context.
    // Mutex is used inside process/ack logic.
    int ret = ldsSmartBaseParseData(data, size);

    return ret;
}

/* ================================ Public API Functions =================== */

int ldsSmartBaseQueryVersion(void)
{
    return ldsSmartBaseSendCommand(LDS_SMART_BASE_CMD_VERSION, RT_NULL, 0);
}

int ldsSmartBaseQueryStatus(void)
{
    return ldsSmartBaseSendCommand(LDS_SMART_BASE_CMD_STATUS, RT_NULL, 0);
}

/**
 * @brief Initialize smart base communication system
 * @details Initializes hardware, UART communication, timers, and state machine
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function performs complete smart base system initialization including:
 *       - Power control pin setup
 *       - UART interface initialization with callback
 *       - Timer configuration for heartbeat and retransmission
 *       - Mutex initialization for thread safety
 *       - State machine and command queue initialization
 */
int ldsSmartBaseInit(void)
{
    rt_err_t result;

    /* Initialize mutex for thread safety */
    result = rt_mutex_init(&g_smartBaseMutex, "smart_base_mutex", RT_IPC_FLAG_PRIO);
    if (result != RT_EOK) {
        LOG_E("Failed to initialize mutex: %d", result);
        return -RT_ERROR;
    }

    /* Initialize power control pin */
    g_smartBasePowerCtrl = power_ctrl_pin_init(SMART_BASE_POWER_CTRL_PIN, PIN_HIGH);
    if (g_smartBasePowerCtrl < 0) {
        LOG_E("Failed to initialize SMART_BASE power control pin %s", SMART_BASE_POWER_CTRL_PIN);
        rt_mutex_detach(&g_smartBaseMutex);
        return -RT_ERROR;
    }

    rt_pin_write(g_smartBasePowerCtrl, PIN_HIGH);

    /* Initialize UART with callback */
    g_smartBaseDev = ldsUartInit(SMART_BASE_SERIAL_NAME, LDS_UART_INDEX_5, ldsSmartBaseProcess);
    if (g_smartBaseDev == RT_NULL) {
        LOG_E("Failed to initialize SMART_BASE UART %s", SMART_BASE_SERIAL_NAME);
        rt_mutex_detach(&g_smartBaseMutex);
        return -RT_ERROR;
    }

    /* Initialize heartbeat timer */
    rt_timer_init(&g_heartbeatTimer, "smart_base_hb",
                  ldsSmartBaseHeartbeatTimeout,
                  RT_NULL,
                  SMART_BASE_HEARTBEAT_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_PERIODIC);
    rt_timer_start(&g_heartbeatTimer);

    /* Initialize retransmission timer */
    rt_timer_init(&g_retransmissionTimer, "smart_base_retry",
                  ldsSmartBaseRetransmissionTimeout,
                  RT_NULL,
                  SMART_BASE_RESPONSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* Initialize parse timeout timer */
    rt_timer_init(&g_parseTimer, "smart_base_parse",
                  ldsSmartBaseParseTimeout,
                  RT_NULL,
                  SMART_BASE_PARSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* Initialize state machine */
    ldsSmartBaseResetStateMachine();
    g_currentSeq = 0;

    /* Initialize command queue */
    ldsSmartBaseInitCmdQueue();


    LOG_I("Smart base communication system initialized successfully");
    return 0;
}

/**
 * @brief Deinitialize smart base communication system
 * @details Cleans up all resources and stops communication
 *
 * @return int 0 on success, negative error code on failure
 */
int ldsSmartBaseDeinit(void)
{
    /* Stop timers */
    rt_timer_stop(&g_heartbeatTimer);
    rt_timer_stop(&g_retransmissionTimer);
    rt_timer_stop(&g_parseTimer);
    rt_timer_detach(&g_heartbeatTimer);
    rt_timer_detach(&g_retransmissionTimer);
    rt_timer_detach(&g_parseTimer);

    /* Reset state machine */
    ldsSmartBaseResetStateMachine();
    
    /* Clear command queue */
    rt_mutex_take(&g_smartBaseMutex, RT_WAITING_FOREVER);
    ldsSmartBaseInitCmdQueue();
    rt_mutex_release(&g_smartBaseMutex);

    /* Close UART device */
    if (g_smartBaseDev != RT_NULL) {
        rt_device_close(g_smartBaseDev);
        g_smartBaseDev = RT_NULL;
    }

    /* Power down device */
    if (g_smartBasePowerCtrl > 0) {
        rt_pin_write(g_smartBasePowerCtrl, PIN_LOW);
        g_smartBasePowerCtrl = -1;
    }

    /* Cleanup mutex */
    rt_mutex_detach(&g_smartBaseMutex);

    LOG_I("Smart base communication system deinitialized");
    return 0;
}

/* ================================ MSH Debug Commands ===================== */

static int ldsSmartBaseQueueStatus(void)
{
    rt_kprintf("Command Queue Status (Size: %d):\n", SMART_BASE_CMD_QUEUE_SIZE);
    bool empty = true;
    for (int i = 0; i < SMART_BASE_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            empty = false;
            rt_kprintf("  Slot %d: [ACTIVE]\n", i);
            rt_kprintf("    seq: %d, cmd: 0x%04X,\n",
                       g_cmdQueue[i].seq, g_cmdQueue[i].cmd);
            rt_kprintf("    retries: %d, sent_at: %u\n",
                       g_cmdQueue[i].retryCount, g_cmdQueue[i].sent_timestamp);
        }
    }
    if (empty) {
        rt_kprintf("  Queue is empty.\n");
    }
    return 0;
}


/**
 * @brief MSH command for smart base operations
 * @details Provides command-line interface for testing smart base communication
 *
 * @param argc Argument count
 * @param argv Argument vector
 * @return int 0 on success, negative error code on failure
 */
static int ldsSmartBaseCmd(int argc, char **argv)
{
    if (argc < 2) {
        rt_kprintf("Usage: smart_base <command> [args...]\n");
        rt_kprintf("Commands:\n");
        rt_kprintf("  init                    - Initialize smart base system\n");
        rt_kprintf("  deinit                  - Deinitialize smart base system\n");
        rt_kprintf("  reset                   - Reset smart base device\n");
        rt_kprintf("  version                 - Query version information\n");
        rt_kprintf("  status                  - Show system status and queue\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "init") == 0) {
        int ret = ldsSmartBaseInit();
        rt_kprintf("Smart base init %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "deinit") == 0) {
        int ret = ldsSmartBaseDeinit();
        rt_kprintf("Smart base deinit %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "reset") == 0) {
        ldsSmartBaseReset();
        rt_kprintf("Smart base device reset\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "version") == 0) {
        int ret = ldsSmartBaseQueryVersion();
        rt_kprintf("Version query %s\n", ret == 0 ? "sent" : "failed");
        return ret;
    }


    if (rt_strcmp(argv[1], "status") == 0) {
        rt_mutex_take(&g_smartBaseMutex, RT_WAITING_FOREVER);
        rt_kprintf("Smart base System Status:\n");
        rt_kprintf("  Device: %s\n", g_smartBaseDev ? "initialized" : "not initialized");
        rt_kprintf("  Power Control: %s\n", g_smartBasePowerCtrl > 0 ? "enabled" : "disabled");
        rt_kprintf("  Current Sequence: %d\n", g_currentSeq);
        rt_kprintf("  RX State: %d\n", g_rxState);
        rt_kprintf("  RX Index: %d\n", g_rxIndex);

        /* Check parse timer state */
        rt_uint8_t parse_timer_state = 0;
        rt_timer_control(&g_parseTimer, RT_TIMER_CTRL_GET_STATE, &parse_timer_state);
        rt_kprintf("  Parse Timer: %s\n", parse_timer_state == RT_TIMER_FLAG_ACTIVATED ? "active" : "inactive");

        ldsSmartBaseQueueStatus();
        rt_mutex_release(&g_smartBaseMutex);
        return 0;
    }

    rt_kprintf("Unknown command: %s\n", argv[1]);
    return -RT_EINVAL;
}

MSH_CMD_EXPORT_ALIAS(ldsSmartBaseCmd, smart_base, Smart base communication protocol commands);
