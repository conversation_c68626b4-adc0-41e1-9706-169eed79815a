#ifndef __APPLICATIONS_LDS_UTILS_H__
#define __APPLICATIONS_LDS_UTILS_H__
/**
 * @brief Initialize the power control pin
 * 
 * @param pin The pin name
 * @param value The initial value to set
 * @return rt_base_t 
 */
rt_base_t power_ctrl_pin_init(const char *pin, rt_uint8_t value);
/**
 * @brief Calculate the SUM checksum of a data buffer
 * 
 * @param data The data buffer
 * @param len The length of the data buffer
 * @return uint8_t The XOR checksum
 */
uint8_t ldsUtilCheckXor(const uint8_t *data, size_t len);
/**
 * @brief Calculate the SUM checksum of a data buffer
 * 
 * @param data The data buffer
 * @param len The length of the data buffer
 * @return uint8_t The SUM checksum
 */
uint8_t ldsUtilCheckSum(const uint8_t *data, size_t len);
/**
 * @brief Reverses the bit order for a given number of bytes within a 32-bit integer.
 *
 * @param dword The input 32-bit integer. The lower bits are used for reversal.
 * @param len The number of bytes (from the least significant end) to consider for bit reversal.
 *            For example, if len is 1, it reverses 8 bits. If 2, it reverses 16 bits.
 *            Valid range for len is 1 to 4.
 * @return The integer with the specified number of bits reversed. On error, returns 0.
 */
uint32_t ldsUtilReverseBits(uint32_t dword, size_t len);
/**
 * @brief Calculate the CRC16 checksum of a data buffer
 * 
 * @param data The data buffer
 * @param len The length of the data buffer
 * @return uint16_t The CRC16 checksum
 */
uint16_t ldsUtilCheckCrc16(const uint8_t *data, size_t len);
#endif // !__APPLICATIONS_LDS_UTILS_H__