/*
 * Copyright (c) 2006-2023, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2018-11-06     balanceTWK   first version
 */

#include <stdint.h>
#include <rthw.h>
#include <rtthread.h>

#include <board.h>
#include <drv_clk.h>

#ifdef BSP_USING_SRAM
    #include "drv_sram.h"
#endif
#ifdef RT_USING_CHERRYUSB
#define N32G45X_USB_BASE (0x40005C00L)
#endif
/**
  * @brief  This function is executed in case of error occurrence.
  * @param  None
  * @retval None
  */
void Error_Handler(void)
{
    /* USER CODE BEGIN Error_Handler */
    /* User can add his own implementation to report the HAL error return state */
    while (1)
    {
    }
    /* USER CODE END Error_Handler */
}

/** System Clock Configuration
*/
void SystemClock_Config(void)
{
    SysTick_Config(SystemCoreClock / RT_TICK_PER_SECOND);
    NVIC_SetPriority(SysTick_IRQn, 0);
}

/**
 * This is the timer interrupt service routine.
 *
 */
void SysTick_Handler(void)
{
    /* enter interrupt */
    rt_interrupt_enter();

    rt_tick_increase();

    /* leave interrupt */
    rt_interrupt_leave();
}
#ifdef RT_USING_CHERRYUSB
/**
 * @brief  This function handles USB WakeUp interrupt request.
 */
void USBWakeUp_IRQHandler(void)
{
    /* enter interrupt */
    rt_interrupt_enter();

    EXTI_ClrITPendBit(EXTI_LINE18);

    /* leave interrupt */
    rt_interrupt_leave();
}
void USB_LP_CAN1_RX0_IRQHandler(void)
{
    /* enter interrupt */
    rt_interrupt_enter();

    extern void USBD_IRQHandler(uint8_t busid);
    USBD_IRQHandler(0);

    /* leave interrupt */
    rt_interrupt_leave();
}
void usb_dc_low_level_init(void)
{
    NVIC_InitType NVIC_InitStructure;
    EXTI_InitType EXTI_InitStructure;
    
    /* 2 bit for pre-emption priority, 2 bits for subpriority */
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);

    /* Enable the USB interrupt */
    NVIC_InitStructure.NVIC_IRQChannel                   = USB_LP_CAN1_RX0_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority        = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd                = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

    /* Enable the USB Wake-up interrupt */
    NVIC_InitStructure.NVIC_IRQChannel                   = USBWakeUp_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_Init(&NVIC_InitStructure);

    /* Configure the EXTI line 18 connected internally to the USB IP */
    EXTI_ClrITPendBit(EXTI_LINE18);
	EXTI_InitStruct(&EXTI_InitStructure);
    EXTI_InitStructure.EXTI_Line    = EXTI_LINE18;
    EXTI_InitStructure.EXTI_Mode    = EXTI_Mode_Interrupt;
    EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Rising;
    EXTI_InitStructure.EXTI_LineCmd = ENABLE;
    EXTI_InitPeripheral(&EXTI_InitStructure);
    /* Select USBCLK source */
    RCC_ConfigUsbClk(RCC_USBCLK_SRC_PLLCLK_DIV3);
    /* Enable the USB clock */
    RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_USB, ENABLE);
}
#define DP_CTRL ((volatile unsigned*)(0x40001820))

#define _EnPortPullup() (*DP_CTRL = (*DP_CTRL) | 0x10000000);
int usbd_init(void)
{
    extern void usbd_app_init(uint8_t busid, uintptr_t reg_base);
    usbd_app_init(0, N32G45X_USB_BASE);
    _EnPortPullup();
    rt_kprintf("USBd init done\n");
    return 0;
}

INIT_APP_EXPORT(usbd_init);
#endif

/**
 * @brief 打印系统复位原因并清除复位标志。
 */
static void print_reset_reason(void)
{
    /* 复位标志定义，需与 n32g45x_rcc.h 中一致 */
    struct {
        uint8_t flag;
        const char *msg;
    } reset_flags[] = {
        {RCC_FLAG_LPWRRST,  "Low Power Reset"},
        {RCC_FLAG_WWDGRST,  "Window Watchdog Reset"},
        {RCC_FLAG_IWDGRST,  "Independent Watchdog Reset"},
        {RCC_FLAG_SFTRST,   "Software Reset"},
        {RCC_FLAG_PORRST,   "Power Down And On Reset"},
        {RCC_FLAG_PINRST,   "Reset Pin Reset"},
        {RCC_FLAG_MMURST,   "Memory Management Unit Reset"},
        {RCC_FLAG_RAMRST,   "Ram Reset"},
        {RCC_FLAG_BKPEMC,   "BackUp EMC Reset"},
        {RCC_FLAG_RETEMC,   "Retention EMC Reset"},
        {RCC_FLAG_BORRST,   "BOR Reset"},
    };
    int found = 0;
    for (size_t i = 0; i < sizeof(reset_flags)/sizeof(reset_flags[0]); i++) {
        if (RCC_GetFlagStatus(reset_flags[i].flag) == SET) {
            rt_kprintf("\n[Reset] %s\n", reset_flags[i].msg);
            found = 1;
        }
    }
    if (!found) {
        rt_kprintf("[Reset] Unknown or No Reset Flag Set\n");
    }
    RCC_ClrFlag();
}

/**
 * This function will initial N32 board.
 */
extern volatile uint32_t _sstack;
void rt_hw_board_init()
{
    //isr stack protect
    _sstack = 0xaa223355;
    /* NVIC Configuration */
#define NVIC_VTOR_MASK              0x3FFFFF80
#ifdef  VECT_TAB_RAM
    /* Set the Vector Table base location at 0x10000000 */
    SCB->VTOR  = (0x10000000 & NVIC_VTOR_MASK);
#else  /* VECT_TAB_FLASH  */
    /* Set the Vector Table base location at 0x08000000 */
    SCB->VTOR  = (0x08000000 & NVIC_VTOR_MASK);
#endif

    SystemClock_Config();
    
#ifdef BSP_USING_SRAM
    rt_system_heap_init((void *)EXT_SRAM_BEGIN, (void *)EXT_SRAM_END);
#else
    rt_system_heap_init((void *)HEAP_BEGIN, (void *)HEAP_END);
#endif
    /* Enable CRC clock */
    RCC_EnableAHBPeriphClk(RCC_AHB_PERIPH_CRC, ENABLE);

#ifdef RT_USING_PIN
    int n32_hw_pin_init(void);
    n32_hw_pin_init();
    n32_msp_jtag_init(RT_NULL);
#endif

#ifdef RT_USING_SERIAL
    int rt_hw_usart_init(void);
    rt_hw_usart_init();
#endif

#if defined(RT_USING_CONSOLE) && defined(RT_USING_DEVICE)
    rt_console_set_device(RT_CONSOLE_DEVICE_NAME);
#endif

#ifdef RT_USING_COMPONENTS_INIT
    rt_components_board_init();
#endif
    
    print_reset_reason();
    
    mcu_boot_process();
}
